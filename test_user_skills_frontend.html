<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>用户技能测试 - MES系统</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 20px;
            background-color: #f5f5f5;
        }
        .container {
            max-width: 800px;
            margin: 0 auto;
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }
        .test-section {
            margin-bottom: 30px;
            padding: 15px;
            border: 1px solid #ddd;
            border-radius: 5px;
        }
        .test-section h3 {
            margin-top: 0;
            color: #333;
        }
        .login-form {
            display: flex;
            gap: 10px;
            margin-bottom: 15px;
        }
        .login-form input {
            padding: 8px;
            border: 1px solid #ddd;
            border-radius: 4px;
        }
        .login-form button {
            padding: 8px 16px;
            background: #007bff;
            color: white;
            border: none;
            border-radius: 4px;
            cursor: pointer;
        }
        .login-form button:hover {
            background: #0056b3;
        }
        .user-info {
            background: #f8f9fa;
            padding: 15px;
            border-radius: 5px;
            margin-top: 15px;
        }
        .skill-tag {
            display: inline-block;
            background: #28a745;
            color: white;
            padding: 4px 8px;
            border-radius: 3px;
            margin: 2px;
            font-size: 12px;
        }
        .error {
            color: #dc3545;
            background: #f8d7da;
            padding: 10px;
            border-radius: 4px;
            margin-top: 10px;
        }
        .success {
            color: #155724;
            background: #d4edda;
            padding: 10px;
            border-radius: 4px;
            margin-top: 10px;
        }
        .test-users {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 15px;
            margin-top: 20px;
        }
        .user-card {
            border: 1px solid #ddd;
            padding: 15px;
            border-radius: 5px;
            background: #f9f9f9;
        }
        .user-card h4 {
            margin-top: 0;
            color: #495057;
        }
        .jwt-info {
            background: #e9ecef;
            padding: 10px;
            border-radius: 4px;
            margin-top: 10px;
            font-family: monospace;
            font-size: 12px;
            word-break: break-all;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🔧 MES系统用户技能测试</h1>
        <p>测试用户技能绑定和JWT token中的技能信息显示</p>

        <div class="test-section">
            <h3>📋 测试用户列表</h3>
            <div class="test-users">
                <div class="user-card">
                    <h4>用户: m001</h4>
                    <p><strong>密码:</strong> m00123</p>
                    <p><strong>预期技能:</strong> Milling (铣床)</p>
                    <button onclick="testLogin('m001', 'm00123')">测试登录</button>
                    <div id="result-m001"></div>
                </div>
                
                <div class="user-card">
                    <h4>用户: cn01</h4>
                    <p><strong>密码:</strong> cn0123</p>
                    <p><strong>预期技能:</strong> CNC Machining (CNC加工)</p>
                    <button onclick="testLogin('cn01', 'cn0123')">测试登录</button>
                    <div id="result-cn01"></div>
                </div>
                
                <div class="user-card">
                    <h4>用户: g001</h4>
                    <p><strong>密码:</strong> g00123</p>
                    <p><strong>预期技能:</strong> Grinding (磨床)</p>
                    <button onclick="testLogin('g001', 'g00123')">测试登录</button>
                    <div id="result-g001"></div>
                </div>
            </div>
        </div>

        <div class="test-section">
            <h3>🧪 自定义登录测试</h3>
            <div class="login-form">
                <input type="text" id="username" placeholder="用户名" value="">
                <input type="password" id="password" placeholder="密码" value="">
                <button onclick="customLogin()">登录测试</button>
            </div>
            <div id="custom-result"></div>
        </div>

        <div class="test-section">
            <h3>📊 测试结果汇总</h3>
            <div id="summary"></div>
        </div>
    </div>

    <script>
        const API_BASE = 'http://localhost:8080/api';
        let testResults = [];

        async function testLogin(username, password) {
            const resultDiv = document.getElementById(`result-${username}`);
            resultDiv.innerHTML = '<p>🔄 正在测试登录...</p>';

            try {
                const response = await fetch(`${API_BASE}/auth/login`, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify({ username, password })
                });

                if (response.ok) {
                    const data = await response.json();
                    const user = data.user;
                    
                    // 解析JWT token
                    const tokenParts = data.token.split('.');
                    const payload = JSON.parse(atob(tokenParts[1]));
                    
                    let html = `
                        <div class="success">✅ 登录成功</div>
                        <div class="user-info">
                            <p><strong>用户ID:</strong> ${user.id}</p>
                            <p><strong>用户名:</strong> ${user.username}</p>
                            <p><strong>姓名:</strong> ${user.full_name || '未设置'}</p>
                            <p><strong>角色:</strong> ${user.roles.join(', ')}</p>
                            <p><strong>技能组:</strong> 
                                ${user.skills.map(skill => `<span class="skill-tag">${skill}</span>`).join('')}
                            </p>
                            <p><strong>状态:</strong> ${user.is_active ? '活跃' : '非活跃'}</p>
                        </div>
                        <div class="jwt-info">
                            <strong>JWT Token 载荷:</strong><br>
                            用户ID: ${payload.sub}<br>
                            用户名: ${payload.username}<br>
                            角色: ${JSON.stringify(payload.roles)}<br>
                            技能: ${JSON.stringify(payload.skills)}<br>
                            过期时间: ${new Date(payload.exp * 1000).toLocaleString()}
                        </div>
                    `;
                    
                    resultDiv.innerHTML = html;
                    
                    // 记录测试结果
                    testResults.push({
                        username,
                        success: true,
                        skills: user.skills,
                        jwtSkills: payload.skills
                    });
                } else {
                    const errorData = await response.json();
                    resultDiv.innerHTML = `<div class="error">❌ 登录失败: ${errorData.message}</div>`;
                    
                    testResults.push({
                        username,
                        success: false,
                        error: errorData.message
                    });
                }
            } catch (error) {
                resultDiv.innerHTML = `<div class="error">❌ 网络错误: ${error.message}</div>`;
                
                testResults.push({
                    username,
                    success: false,
                    error: error.message
                });
            }
            
            updateSummary();
        }

        async function customLogin() {
            const username = document.getElementById('username').value;
            const password = document.getElementById('password').value;
            
            if (!username || !password) {
                document.getElementById('custom-result').innerHTML = 
                    '<div class="error">请输入用户名和密码</div>';
                return;
            }
            
            await testLogin(username, password);
            document.getElementById('custom-result').innerHTML = 
                `<p>测试结果已显示在结果汇总中</p>`;
        }

        function updateSummary() {
            const summaryDiv = document.getElementById('summary');
            
            if (testResults.length === 0) {
                summaryDiv.innerHTML = '<p>暂无测试结果</p>';
                return;
            }
            
            const successful = testResults.filter(r => r.success);
            const failed = testResults.filter(r => !r.success);
            
            let html = `
                <p><strong>测试统计:</strong></p>
                <ul>
                    <li>成功: ${successful.length}</li>
                    <li>失败: ${failed.length}</li>
                    <li>总计: ${testResults.length}</li>
                </ul>
            `;
            
            if (successful.length > 0) {
                html += '<h4>✅ 成功的登录:</h4><ul>';
                successful.forEach(result => {
                    html += `<li><strong>${result.username}:</strong> 技能 ${JSON.stringify(result.skills)}</li>`;
                });
                html += '</ul>';
            }
            
            if (failed.length > 0) {
                html += '<h4>❌ 失败的登录:</h4><ul>';
                failed.forEach(result => {
                    html += `<li><strong>${result.username}:</strong> ${result.error}</li>`;
                });
                html += '</ul>';
            }
            
            summaryDiv.innerHTML = html;
        }

        // 页面加载完成后的初始化
        document.addEventListener('DOMContentLoaded', function() {
            console.log('MES用户技能测试页面已加载');
            updateSummary();
        });
    </script>
</body>
</html>
