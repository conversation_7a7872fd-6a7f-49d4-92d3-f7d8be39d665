<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>生产执行中心技能组显示修复测试</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .container {
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
            margin-bottom: 20px;
        }
        .user-section {
            display: flex;
            gap: 20px;
            margin-bottom: 20px;
        }
        .user-card {
            flex: 1;
            padding: 15px;
            border: 1px solid #ddd;
            border-radius: 5px;
            background: #f9f9f9;
        }
        .login-btn {
            background: #1890ff;
            color: white;
            border: none;
            padding: 8px 16px;
            border-radius: 4px;
            cursor: pointer;
            margin-top: 10px;
        }
        .login-btn:hover {
            background: #40a9ff;
        }
        .test-btn {
            background: #52c41a;
            color: white;
            border: none;
            padding: 8px 16px;
            border-radius: 4px;
            cursor: pointer;
            margin: 5px;
        }
        .test-btn:hover {
            background: #73d13d;
        }
        .result {
            background: #f6f6f6;
            padding: 15px;
            border-radius: 4px;
            margin-top: 10px;
            white-space: pre-wrap;
            font-family: monospace;
            max-height: 400px;
            overflow-y: auto;
        }
        .error {
            background: #fff2f0;
            border: 1px solid #ffccc7;
            color: #a8071a;
        }
        .success {
            background: #f6ffed;
            border: 1px solid #b7eb8f;
            color: #389e0d;
        }
        .info {
            background: #e6f7ff;
            border: 1px solid #91d5ff;
            color: #0050b3;
        }
        .current-user {
            background: #fff7e6;
            border: 1px solid #ffd591;
            padding: 10px;
            border-radius: 4px;
            margin-bottom: 20px;
        }
        .skill-display {
            background: #f0f5ff;
            border: 1px solid #adc6ff;
            padding: 15px;
            border-radius: 4px;
            margin: 10px 0;
        }
        .skill-display h3 {
            margin: 0 0 10px 0;
            color: #722ed1;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🔧 生产执行中心技能组显示修复测试</h1>
        <p>测试修复后的技能组显示逻辑</p>
        
        <div id="currentUser" class="current-user" style="display: none;">
            <strong>当前登录用户：</strong><span id="currentUserInfo"></span>
        </div>

        <div class="user-section">
            <div class="user-card">
                <h3>🔩 磨床用户 (g001)</h3>
                <p><strong>用户名:</strong> g001</p>
                <p><strong>密码:</strong> g00123</p>
                <p><strong>预期技能组:</strong> Grinding</p>
                <button class="login-btn" onclick="loginAndTest('g001', 'g00123')">登录并测试</button>
            </div>
            
            <div class="user-card">
                <h3>🏭 CNC用户 (cn01)</h3>
                <p><strong>用户名:</strong> cn01</p>
                <p><strong>密码:</strong> cn0123</p>
                <p><strong>预期技能组:</strong> CNC Machining</p>
                <button class="login-btn" onclick="loginAndTest('cn01', 'cn0123')">登录并测试</button>
            </div>
            
            <div class="user-card">
                <h3>⚙️ 测试用户 (m001)</h3>
                <p><strong>用户名:</strong> m001</p>
                <p><strong>密码:</strong> m00123</p>
                <p><strong>预期技能组:</strong> Milling</p>
                <button class="login-btn" onclick="loginAndTest('m001', 'm00123')">登录并测试</button>
            </div>
        </div>

        <div id="skillDisplay" class="skill-display" style="display: none;">
            <h3>🎯 技能组显示测试结果</h3>
            <div id="skillInfo"></div>
        </div>

        <div class="container">
            <h2>🧪 测试结果</h2>
            <button class="test-btn" onclick="openProductionCenter()">打开生产执行中心</button>
            <button class="test-btn" onclick="testSkillGroupDisplay()">测试技能组显示逻辑</button>
            
            <div id="testResult" class="result"></div>
        </div>
    </div>

    <script>
        const API_BASE = 'http://localhost:8080/api';
        let currentToken = null;
        let currentUser = null;

        function log(message, type = 'info') {
            const resultDiv = document.getElementById('testResult');
            const timestamp = new Date().toLocaleTimeString();
            const className = type === 'error' ? 'error' : type === 'success' ? 'success' : 'info';
            resultDiv.innerHTML = `<div class="${className}">[${timestamp}] ${message}</div>` + resultDiv.innerHTML;
        }

        async function loginAndTest(username, password) {
            try {
                log(`正在登录用户: ${username}...`);
                
                const response = await fetch(`${API_BASE}/auth/login`, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify({ username, password })
                });

                if (!response.ok) {
                    throw new Error(`登录失败: ${response.status} ${response.statusText}`);
                }

                const data = await response.json();
                currentToken = data.token;
                currentUser = data.user;
                
                // 显示当前用户信息
                const userDiv = document.getElementById('currentUser');
                const userInfo = document.getElementById('currentUserInfo');
                userInfo.textContent = `${username} (${currentUser.full_name || '无姓名'})`;
                userDiv.style.display = 'block';
                
                log(`✅ 登录成功: ${username}`, 'success');
                
                // 显示技能组信息
                displaySkillGroups();
                
                // 自动测试技能组显示逻辑
                await testSkillGroupDisplay();
                
            } catch (error) {
                log(`❌ 登录失败: ${error.message}`, 'error');
            }
        }

        function displaySkillGroups() {
            const skillDiv = document.getElementById('skillDisplay');
            const skillInfo = document.getElementById('skillInfo');
            
            if (currentUser && currentUser.skills) {
                skillDiv.style.display = 'block';
                
                const skillsHtml = `
                    <p><strong>用户技能组:</strong></p>
                    <ul>
                        ${currentUser.skills.map(skill => `<li>${skill}</li>`).join('')}
                    </ul>
                    <p><strong>技能组数量:</strong> ${currentUser.skills.length}</p>
                    <p><strong>显示逻辑测试:</strong></p>
                    <div style="background: #fff; padding: 10px; border-radius: 4px; border: 1px solid #d9d9d9;">
                        <strong>修复前:</strong> myTasks?.[0]?.skill_group_name || '未分配'<br>
                        <strong>修复后:</strong> user?.skills && user.skills.length > 0 ? user.skills.join(', ') : '未分配'<br>
                        <strong>实际显示:</strong> <span style="color: #722ed1; font-weight: bold;">${currentUser.skills.length > 0 ? currentUser.skills.join(', ') : '未分配'}</span>
                    </div>
                `;
                
                skillInfo.innerHTML = skillsHtml;
            } else {
                skillDiv.style.display = 'block';
                skillInfo.innerHTML = '<p style="color: #ff4d4f;">❌ 用户没有技能组信息</p>';
            }
        }

        async function testSkillGroupDisplay() {
            if (!currentUser) {
                log('❌ 请先登录', 'error');
                return;
            }

            log('🧪 测试技能组显示逻辑...');
            
            // 模拟修复前的逻辑
            const oldLogic = "myTasks?.[0]?.skill_group_name || '未分配'";
            
            // 模拟修复后的逻辑
            const newLogic = currentUser.skills && currentUser.skills.length > 0 ? 
                currentUser.skills.join(', ') : '未分配';
            
            log(`📊 技能组显示测试结果:`, 'info');
            log(`   用户: ${currentUser.username} (${currentUser.full_name || '无姓名'})`, 'info');
            log(`   技能组数据: ${JSON.stringify(currentUser.skills)}`, 'info');
            log(`   修复前逻辑: ${oldLogic} → 依赖任务数据，可能显示"未分配"`, 'info');
            log(`   修复后逻辑: ${newLogic}`, 'success');
            
            if (currentUser.skills && currentUser.skills.length > 0) {
                log(`✅ 修复成功！现在正确显示用户的技能组: ${newLogic}`, 'success');
            } else {
                log(`⚠️ 用户没有分配技能组`, 'error');
            }
        }

        function openProductionCenter() {
            if (!currentToken) {
                log('❌ 请先登录', 'error');
                return;
            }
            
            log('🚀 打开生产执行中心页面...', 'info');
            window.open('http://localhost:3001/production-center', '_blank');
        }

        // 页面加载时的初始化
        window.onload = function() {
            log('🚀 生产执行中心技能组显示修复测试页面已加载');
            log('请选择一个用户登录并测试技能组显示修复效果');
        };
    </script>
</body>
</html>
